/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/signin/page";
exports.ids = ["app/signin/page"];
exports.modules = {

/***/ "(action-browser)/./app/actions/auth.js":
/*!*****************************!*\
  !*** ./app/actions/auth.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signInWithGoogle: () => (/* binding */ signInWithGoogle),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(action-browser)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00537944a011904042022ffc65d8c312273cca4c34\":\"getSession\",\"00d123b98cbfcf9a7f6187d177c0f4cb557a5b202b\":\"signOut\",\"00e4c274ab1078fef922f29cf08fd4aefe0f6a5b22\":\"signInWithGoogle\",\"40387f51c039c5267bc6e8ef38ad5b906a4889d772\":\"signUp\",\"602b2deae922dbdda09b71b8cd87766a99535083f4\":\"signIn\"} */ \n\n\n\n\n// Get session server-side\nasync function getSession() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://zzkytozgnociyjvhthfk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\", {\n        cookies: {\n            get: (name)=>cookieStore.get(name)?.value,\n            set: ()=>{},\n            remove: ()=>{}\n        }\n    });\n    return supabase.auth.getSession();\n}\n// Sign up with email/password\nasync function signUp({ email, password, name }) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.headers)();\n    const origin = headersList.get('origin') || process.env.APP_URL || 'http://localhost:3000';\n    // Basic validation\n    if (!email || !password) {\n        return {\n            success: false,\n            message: 'Email and password are required'\n        };\n    }\n    if (password.length < 8) {\n        return {\n            success: false,\n            message: 'Password must be at least 8 characters long'\n        };\n    }\n    try {\n        console.log(`Attempting sign up for email: ${email}, password: ${'*'.repeat(password?.length || 0)}`);\n        // Create the server client with cookie handling for server actions\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://zzkytozgnociyjvhthfk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\", {\n            cookies: {\n                get: (name)=>{\n                    const cookie = cookieStore.get(name);\n                    return cookie?.value;\n                },\n                set: (name, value, options)=>{\n                    const isProduction = \"development\" === 'production';\n                    const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options,\n                        secure: isProduction,\n                        maxAge,\n                        path: options?.path || '/'\n                    });\n                },\n                remove: (name, options)=>{\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        maxAge: -1,\n                        ...options,\n                        path: options?.path || '/'\n                    });\n                }\n            }\n        });\n        // Construct the email redirect URL for confirmation\n        const emailRedirectTo = `${origin}/dashboard`;\n        console.log(`Email redirect URL set to: ${emailRedirectTo}`);\n        // Attempt sign up\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                emailRedirectTo,\n                data: {\n                    name: name || email.split('@')[0]\n                }\n            }\n        });\n        if (error) {\n            console.error('Supabase auth error during sign up:', error);\n            return {\n                success: false,\n                message: error.message\n            };\n        }\n        // Handle different scenarios\n        if (data.user && !data.session) {\n            console.log('Sign up successful, email confirmation required');\n            return {\n                success: true,\n                needsEmailConfirmation: true,\n                message: 'Sign up successful. Please check your email for confirmation.'\n            };\n        } else if (data.user && data.session) {\n            console.log('Sign up successful, user created and logged in immediately');\n            return {\n                success: true,\n                needsEmailConfirmation: false,\n                message: 'Account created successfully. You are now logged in.'\n            };\n        } else {\n            console.warn('Unexpected response format from Supabase sign up');\n            return {\n                success: false,\n                message: 'An unexpected error occurred during sign up'\n            };\n        }\n    } catch (err) {\n        console.error('Unexpected error during sign up:', err);\n        return {\n            success: false,\n            message: 'An unexpected error occurred during sign up'\n        };\n    }\n}\n// Sign in with email/password\nasync function signIn(email, password) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    try {\n        // Log the input parameters (mask password for security)\n        console.log(`Attempting sign in for email: ${email}, password: ${'*'.repeat(password?.length || 0)}`);\n        console.log('Environment check - NEXT_PUBLIC_SUPABASE_URL:', \"https://zzkytozgnociyjvhthfk.supabase.co\");\n        console.log('Environment check - NEXT_PUBLIC_SUPABASE_ANON_KEY available:', !!\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\");\n        // Create the server client with improved cookie handling\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://zzkytozgnociyjvhthfk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\", {\n            cookies: {\n                get: (name)=>{\n                    const cookie = cookieStore.get(name);\n                    console.log(`Cookie get: ${name}`, cookie ? 'exists' : 'not found');\n                    return cookie?.value;\n                },\n                set: (name, value, options)=>{\n                    // Ensure the secure flag is set according to environment\n                    // For local development, secure should typically be false\n                    const isProduction = \"development\" === 'production';\n                    const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days\n                    console.log(`Setting cookie: ${name}, maxAge: ${maxAge}, secure: ${isProduction}`);\n                    try {\n                        cookieStore.set({\n                            name,\n                            value,\n                            ...options,\n                            secure: isProduction,\n                            maxAge,\n                            path: options?.path || '/'\n                        });\n                    } catch (error) {\n                        console.error(`Error setting cookie ${name}:`, error);\n                    }\n                },\n                remove: (name, options)=>{\n                    console.log(`Removing cookie: ${name}`);\n                    try {\n                        cookieStore.set({\n                            name,\n                            value: '',\n                            maxAge: -1,\n                            ...options,\n                            path: options?.path || '/'\n                        });\n                    } catch (error) {\n                        console.error(`Error removing cookie ${name}:`, error);\n                    }\n                }\n            }\n        });\n        // Log authentication attempt\n        console.log('Supabase client created, attempting signInWithPassword');\n        // Attempt sign in\n        const result = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        // Log the result (exclude sensitive data)\n        if (result.error) {\n            console.error('Supabase auth error:', result.error);\n            // Return a serializable error object\n            return {\n                success: false,\n                error: {\n                    message: result.error.message || 'Authentication failed'\n                }\n            };\n        } else {\n            console.log('Auth successful, session established');\n            // Log session details (excluding sensitive data)\n            const { data: { session } } = await supabase.auth.getSession();\n            console.log('Session user ID:', session?.user?.id);\n            console.log('Session expires at:', session?.expires_at);\n            // Ensure cookies are properly set for the session\n            if (session) {\n                // Force refresh the session to ensure cookies are properly set\n                await supabase.auth.refreshSession();\n            }\n            // Return a serializable success object\n            return {\n                success: true,\n                user: {\n                    id: session?.user?.id,\n                    email: session?.user?.email,\n                    name: session?.user?.user_metadata?.name || ''\n                }\n            };\n        }\n    } catch (err) {\n        console.error('Unexpected error during sign in:', err);\n        // Return a serializable error object\n        return {\n            success: false,\n            error: {\n                message: 'An unexpected error occurred during sign in'\n            }\n        };\n    }\n}\n// Sign in with Google OAuth\nasync function signInWithGoogle() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.headers)();\n    const origin = headersList.get('origin') || process.env.APP_URL || 'http://localhost:3000';\n    try {\n        console.log('Attempting sign in with Google OAuth');\n        // Create the server client with cookie handling\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://zzkytozgnociyjvhthfk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\", {\n            cookies: {\n                get: (name)=>{\n                    const cookie = cookieStore.get(name);\n                    return cookie?.value;\n                },\n                set: (name, value, options)=>{\n                    const isProduction = \"development\" === 'production';\n                    const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options,\n                        secure: isProduction,\n                        maxAge,\n                        path: options?.path || '/'\n                    });\n                },\n                remove: (name, options)=>{\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        maxAge: -1,\n                        ...options,\n                        path: options?.path || '/'\n                    });\n                }\n            }\n        });\n        // Construct the redirect URL\n        const redirectTo = `${origin}/auth/callback`;\n        console.log(`Google OAuth redirect URL set to: ${redirectTo}`);\n        // Initiate Google OAuth sign in\n        const { data, error } = await supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo,\n                queryParams: {\n                    access_type: 'offline',\n                    prompt: 'consent'\n                }\n            }\n        });\n        if (error) {\n            console.error('Supabase auth error during Google sign in:', error);\n            return {\n                success: false,\n                error: {\n                    message: error.message || 'Authentication with Google failed'\n                }\n            };\n        }\n        // Return the URL to redirect to\n        return {\n            success: true,\n            url: data.url\n        };\n    } catch (err) {\n        console.error('Unexpected error during Google sign in:', err);\n        return {\n            success: false,\n            error: {\n                message: 'An unexpected error occurred during Google sign in'\n            }\n        };\n    }\n}\n// Sign out\nasync function signOut() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createServerClient)(\"https://zzkytozgnociyjvhthfk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\", {\n        cookies: {\n            get: (name)=>cookieStore.get(name)?.value,\n            set: (name, value, options)=>{\n                cookieStore.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove: (name, options)=>{\n                cookieStore.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    return supabase.auth.signOut();\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    getSession,\n    signUp,\n    signIn,\n    signInWithGoogle,\n    signOut\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getSession, \"00537944a011904042022ffc65d8c312273cca4c34\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(signUp, \"40387f51c039c5267bc6e8ef38ad5b906a4889d772\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(signIn, \"602b2deae922dbdda09b71b8cd87766a99535083f4\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(signInWithGoogle, \"00e4c274ab1078fef922f29cf08fd4aefe0f6a5b22\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(signOut, \"00d123b98cbfcf9a7f6187d177c0f4cb557a5b202b\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./app/actions/auth.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%7B%22id%22%3A%2200537944a011904042022ffc65d8c312273cca4c34%22%2C%22exportedName%22%3A%22getSession%22%7D%2C%7B%22id%22%3A%2200d123b98cbfcf9a7f6187d177c0f4cb557a5b202b%22%2C%22exportedName%22%3A%22signOut%22%7D%2C%7B%22id%22%3A%2200e4c274ab1078fef922f29cf08fd4aefe0f6a5b22%22%2C%22exportedName%22%3A%22signInWithGoogle%22%7D%2C%7B%22id%22%3A%2240387f51c039c5267bc6e8ef38ad5b906a4889d772%22%2C%22exportedName%22%3A%22signUp%22%7D%2C%7B%22id%22%3A%22602b2deae922dbdda09b71b8cd87766a99535083f4%22%2C%22exportedName%22%3A%22signIn%22%7D%5D%5D%5D&__client_imported__=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%7B%22id%22%3A%2200537944a011904042022ffc65d8c312273cca4c34%22%2C%22exportedName%22%3A%22getSession%22%7D%2C%7B%22id%22%3A%2200d123b98cbfcf9a7f6187d177c0f4cb557a5b202b%22%2C%22exportedName%22%3A%22signOut%22%7D%2C%7B%22id%22%3A%2200e4c274ab1078fef922f29cf08fd4aefe0f6a5b22%22%2C%22exportedName%22%3A%22signInWithGoogle%22%7D%2C%7B%22id%22%3A%2240387f51c039c5267bc6e8ef38ad5b906a4889d772%22%2C%22exportedName%22%3A%22signUp%22%7D%2C%7B%22id%22%3A%22602b2deae922dbdda09b71b8cd87766a99535083f4%22%2C%22exportedName%22%3A%22signIn%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00537944a011904042022ffc65d8c312273cca4c34\": () => (/* reexport safe */ C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__.getSession),\n/* harmony export */   \"00d123b98cbfcf9a7f6187d177c0f4cb557a5b202b\": () => (/* reexport safe */ C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__.signOut),\n/* harmony export */   \"00e4c274ab1078fef922f29cf08fd4aefe0f6a5b22\": () => (/* reexport safe */ C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__.signInWithGoogle),\n/* harmony export */   \"40387f51c039c5267bc6e8ef38ad5b906a4889d772\": () => (/* reexport safe */ C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__.signUp),\n/* harmony export */   \"602b2deae922dbdda09b71b8cd87766a99535083f4\": () => (/* reexport safe */ C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__.signIn)\n/* harmony export */ });\n/* harmony import */ var C_Users_amerk_Documents_callsaver_app_frontend_app_actions_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/actions/auth.js */ \"(action-browser)/./app/actions/auth.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%7B%22id%22%3A%2200537944a011904042022ffc65d8c312273cca4c34%22%2C%22exportedName%22%3A%22getSession%22%7D%2C%7B%22id%22%3A%2200d123b98cbfcf9a7f6187d177c0f4cb557a5b202b%22%2C%22exportedName%22%3A%22signOut%22%7D%2C%7B%22id%22%3A%2200e4c274ab1078fef922f29cf08fd4aefe0f6a5b22%22%2C%22exportedName%22%3A%22signInWithGoogle%22%7D%2C%7B%22id%22%3A%2240387f51c039c5267bc6e8ef38ad5b906a4889d772%22%2C%22exportedName%22%3A%22signUp%22%7D%2C%7B%22id%22%3A%22602b2deae922dbdda09b71b8cd87766a99535083f4%22%2C%22exportedName%22%3A%22signIn%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ClientServiceWorkerManager.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ClientServiceWorkerManager.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConditionalNavbar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConditionalNavbar.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/ConsoleErrorSuppressor.js":
/*!**************************************************!*\
  !*** ./app/components/ConsoleErrorSuppressor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConsoleErrorSuppressor.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConsoleErrorSuppressor.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\GlobalBackgroundOverlay.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\GlobalBackgroundOverlay.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/SafeMetaMaskDetection.js":
/*!*************************************************!*\
  !*** ./app/components/SafeMetaMaskDetection.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\SafeMetaMaskDetection.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\SafeMetaMaskDetection.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e66cafe106\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MGU2NmNhZmUxMDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\i18n\\LanguageContext.jsx",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\i18n\\LanguageContext.jsx",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ClientServiceWorkerManager */ \"(rsc)/./app/components/ClientServiceWorkerManager.js\");\n/* harmony import */ var _components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ConditionalNavbar */ \"(rsc)/./app/components/ConditionalNavbar.jsx\");\n/* harmony import */ var _components_ConsoleErrorSuppressor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/ConsoleErrorSuppressor */ \"(rsc)/./app/components/ConsoleErrorSuppressor.js\");\n/* harmony import */ var _components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/GlobalBackgroundOverlay */ \"(rsc)/./app/components/GlobalBackgroundOverlay.jsx\");\n/* harmony import */ var _components_SafeMetaMaskDetection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/SafeMetaMaskDetection */ \"(rsc)/./app/components/SafeMetaMaskDetection.js\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./i18n/LanguageContext */ \"(rsc)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(rsc)/./app/providers/SessionProvider.jsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    title: 'CallSaver - Never Miss A Customer Call Again',\n    description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',\n    keywords: [\n        'AI call management',\n        'automated calls',\n        'business phone system',\n        'call automation',\n        'voice AI'\n    ],\n    authors: [\n        {\n            name: 'CallSaver Team'\n        }\n    ],\n    creator: 'CallSaver',\n    publisher: 'CallSaver',\n    icons: {\n        icon: '/favicon.svg',\n        shortcut: '/favicon.svg',\n        apple: '/favicon.svg'\n    },\n    openGraph: {\n        title: 'CallSaver - Never Miss A Customer Call Again',\n        description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',\n        type: 'website',\n        siteName: 'CallSaver',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'CallSaver - AI-Powered Call Management Platform'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'CallSaver - Never Miss A Customer Call Again',\n        description: 'AI-Powered Call Management Platform that automates your calls with advanced AI.',\n        images: [\n            '/og-image.jpg'\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    themeColor: '#0d0d17'\n};\nconst revalidate = 3600;\nfunction RootLayout({ children }) {\n    // Get nonce from headers for CSP\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const nonce = headersList.get('x-nonce') || '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: nonce && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"csp-nonce\",\n                    content: nonce\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default().className)} bg-[#0d0d17] min-h-screen overflow-x-hidden`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConsoleErrorSuppressor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SafeMetaMaskDetection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_9__.SessionProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_8__.LanguageProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen flex flex-col relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                            className: \"flex-grow relative\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSession: () => (/* binding */ useSession)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"SessionProvider",
);const useSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"useSession",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/signin/page.js":
/*!****************************!*\
  !*** ./app/signin/page.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\signin\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignin%2Fpage&page=%2Fsignin%2Fpage&appPaths=%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fsignin%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignin%2Fpage&page=%2Fsignin%2Fpage&appPaths=%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fsignin%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/signin/page.js */ \"(rsc)/./app/signin/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'signin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/signin/page\",\n        pathname: \"/signin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignin%2Fpage&page=%2Fsignin%2Fpage&appPaths=%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fsignin%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ClientServiceWorkerManager.js */ \"(rsc)/./app/components/ClientServiceWorkerManager.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConditionalNavbar.jsx */ \"(rsc)/./app/components/ConditionalNavbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConsoleErrorSuppressor.js */ \"(rsc)/./app/components/ConsoleErrorSuppressor.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/GlobalBackgroundOverlay.jsx */ \"(rsc)/./app/components/GlobalBackgroundOverlay.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SafeMetaMaskDetection.js */ \"(rsc)/./app/components/SafeMetaMaskDetection.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(rsc)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(rsc)/./app/providers/SessionProvider.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/signin/page.js */ \"(rsc)/./app/signin/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDY2FsbHNhdmVyLmFwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDc2lnbmluJTVDJTVDcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcY2FsbHNhdmVyLmFwcFxcXFxmcm9udGVuZFxcXFxhcHBcXFxcc2lnbmluXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/actions/auth.js":
/*!*****************************!*\
  !*** ./app/actions/auth.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signInWithGoogle: () => (/* binding */ signInWithGoogle),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"00537944a011904042022ffc65d8c312273cca4c34\":\"getSession\",\"00d123b98cbfcf9a7f6187d177c0f4cb557a5b202b\":\"signOut\",\"00e4c274ab1078fef922f29cf08fd4aefe0f6a5b22\":\"signInWithGoogle\",\"40387f51c039c5267bc6e8ef38ad5b906a4889d772\":\"signUp\",\"602b2deae922dbdda09b71b8cd87766a99535083f4\":\"signIn\"} */ \nvar getSession = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00537944a011904042022ffc65d8c312273cca4c34\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getSession\");\nvar signUp = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40387f51c039c5267bc6e8ef38ad5b906a4889d772\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"signUp\");\nvar signIn = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"602b2deae922dbdda09b71b8cd87766a99535083f4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"signIn\");\nvar signInWithGoogle = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00e4c274ab1078fef922f29cf08fd4aefe0f6a5b22\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"signInWithGoogle\");\nvar signOut = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00d123b98cbfcf9a7f6187d177c0f4cb557a5b202b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"signOut\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/actions/auth.js\n");

/***/ }),

/***/ "(ssr)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientServiceWorkerManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ClientServiceWorkerManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ClientServiceWorkerManager.useEffect\": ()=>{\n            // Unregister service workers in development to prevent caching issues\n            // and \"Frame with ID was removed\" errors during hot reloading.\n            if (false) {}\n        }\n    }[\"ClientServiceWorkerManager.useEffect\"], []);\n    // This component doesn't render anything\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9DbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7NkRBRWtDO0FBRW5CLFNBQVNDO0lBQ3RCRCxnREFBU0E7Z0RBQUM7WUFDUixzRUFBc0U7WUFDdEUsK0RBQStEO1lBQy9ELElBQUksS0FBNkRFLEVBQUUsRUFRbEU7UUFDSDsrQ0FBRyxFQUFFO0lBRUwseUNBQXlDO0lBQ3pDLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbWVya1xcRG9jdW1lbnRzXFxjYWxsc2F2ZXIuYXBwXFxmcm9udGVuZFxcYXBwXFxjb21wb25lbnRzXFxDbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBVbnJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VycyBpbiBkZXZlbG9wbWVudCB0byBwcmV2ZW50IGNhY2hpbmcgaXNzdWVzXG4gICAgLy8gYW5kIFwiRnJhbWUgd2l0aCBJRCB3YXMgcmVtb3ZlZFwiIGVycm9ycyBkdXJpbmcgaG90IHJlbG9hZGluZy5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcikge1xuICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIuZ2V0UmVnaXN0cmF0aW9ucygpLnRoZW4oKHJlZ2lzdHJhdGlvbnMpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCByZWdpc3RyYXRpb24gb2YgcmVnaXN0cmF0aW9ucykge1xuICAgICAgICAgIHJlZ2lzdHJhdGlvbi51bnJlZ2lzdGVyKCk7XG4gICAgICAgIH1cbiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTZXJ2aWNlIFdvcmtlciB1bnJlZ2lzdHJhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gVGhpcyBjb21wb25lbnQgZG9lc24ndCByZW5kZXIgYW55dGhpbmdcbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiQ2xpZW50U2VydmljZVdvcmtlck1hbmFnZXIiLCJuYXZpZ2F0b3IiLCJzZXJ2aWNlV29ya2VyIiwiZ2V0UmVnaXN0cmF0aW9ucyIsInRoZW4iLCJyZWdpc3RyYXRpb25zIiwicmVnaXN0cmF0aW9uIiwidW5yZWdpc3RlciIsImNhdGNoIiwiZXJyb3IiLCJjb25zb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ClientServiceWorkerManager.js\n");

/***/ }),

/***/ "(ssr)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConditionalNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./app/components/Navbar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ConditionalNavbar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Apply padding to main content only when navbar is visible\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ConditionalNavbar.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"ConditionalNavbar.useEffect\"], [\n        pathname\n    ]);\n    // Don't show the navbar on dashboard pages\n    if (pathname && pathname.startsWith('/dashboard')) {\n        return null;\n    }\n    // Show navbar on non-dashboard pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConditionalNavbar.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConditionalNavbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ConsoleErrorSuppressor.js":
/*!**************************************************!*\
  !*** ./app/components/ConsoleErrorSuppressor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Console Error Suppressor Component\n * Filters out known non-critical console errors to reduce noise during development\n */ const ConsoleErrorSuppressor = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ConsoleErrorSuppressor.useEffect\": ()=>{\n            // Only run in development mode\n            if (false) {}\n            // Store original console methods\n            const originalError = console.error;\n            const originalWarn = console.warn;\n            // List of error patterns to suppress (non-critical errors)\n            const suppressPatterns = [\n                /The message port closed before a response was received/,\n                /MetaMask extension not found/,\n                /ChromeTransport.*connectChrome error/,\n                /contentscript\\.bundle\\.js/,\n                /chrome-extension:/,\n                /moz-extension:/\n            ];\n            // Custom error handler\n            const filteredError = {\n                \"ConsoleErrorSuppressor.useEffect.filteredError\": (...args)=>{\n                    const message = args.join(' ');\n                    // Check if this error should be suppressed\n                    const shouldSuppress = suppressPatterns.some({\n                        \"ConsoleErrorSuppressor.useEffect.filteredError.shouldSuppress\": (pattern)=>pattern.test(message)\n                    }[\"ConsoleErrorSuppressor.useEffect.filteredError.shouldSuppress\"]);\n                    // Only log if not suppressed\n                    if (!shouldSuppress) {\n                        originalError.apply(console, args);\n                    } else {\n                        // Optionally log suppressed errors in a different way for debugging\n                        console.debug('Suppressed non-critical error:', message);\n                    }\n                }\n            }[\"ConsoleErrorSuppressor.useEffect.filteredError\"];\n            // Custom warning handler\n            const filteredWarn = {\n                \"ConsoleErrorSuppressor.useEffect.filteredWarn\": (...args)=>{\n                    const message = args.join(' ');\n                    // Check if this warning should be suppressed\n                    const shouldSuppress = suppressPatterns.some({\n                        \"ConsoleErrorSuppressor.useEffect.filteredWarn.shouldSuppress\": (pattern)=>pattern.test(message)\n                    }[\"ConsoleErrorSuppressor.useEffect.filteredWarn.shouldSuppress\"]);\n                    // Only log if not suppressed\n                    if (!shouldSuppress) {\n                        originalWarn.apply(console, args);\n                    }\n                }\n            }[\"ConsoleErrorSuppressor.useEffect.filteredWarn\"];\n            // Override console methods\n            console.error = filteredError;\n            console.warn = filteredWarn;\n            // Cleanup function to restore original console methods\n            return ({\n                \"ConsoleErrorSuppressor.useEffect\": ()=>{\n                    console.error = originalError;\n                    console.warn = originalWarn;\n                }\n            })[\"ConsoleErrorSuppressor.useEffect\"];\n        }\n    }[\"ConsoleErrorSuppressor.useEffect\"], []);\n    // This component doesn't render anything\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConsoleErrorSuppressor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConsoleErrorSuppressor.js\n");

/***/ }),

/***/ "(ssr)/./app/components/FallingIcons.jsx":
/*!*****************************************!*\
  !*** ./app/components/FallingIcons.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Define icons outside component to prevent recreation on every render\nconst DEFAULT_ICONS = [\n    // Communication & Business Icons\n    '✉️',\n    '📱',\n    '💬',\n    '🔔',\n    '📞',\n    '🗓️',\n    '📊',\n    '💼',\n    '📧',\n    '📨',\n    '📩',\n    '💌',\n    '📮',\n    '📬',\n    '📭',\n    '📯',\n    '☎️',\n    '📟',\n    '📠',\n    '📺',\n    '📻',\n    '🎙️',\n    '🎧',\n    '🔊',\n    '💻',\n    '🖥️',\n    '⌨️',\n    '🖱️',\n    '🖨️',\n    '💾',\n    '💿',\n    '📀',\n    '📈',\n    '📉',\n    '📋',\n    '📌',\n    '📍',\n    '🎯',\n    '💡',\n    '🔍',\n    '⚡',\n    '🌟',\n    '✨',\n    '💫',\n    '🔥',\n    '💎',\n    '🎨',\n    '🚀',\n    // Geometric shapes for variety\n    '🔷',\n    '🔶',\n    '🔸',\n    '🔹',\n    '🟦',\n    '🟧',\n    '🟨',\n    '🟩'\n];\n/**\n * FallingIcons component - Enhanced with better visibility and performance\n * Creates a background animation of falling icons/particles\n */ function FallingIcons({ count = 12, icons = DEFAULT_ICONS }) {\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Generate particles only once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FallingIcons.useEffect\": ()=>{\n            setIsClient(true);\n            // Enhanced particle generation with better visibility\n            const newParticles = [];\n            // Calculate responsive count inline to avoid dependency issues\n            const width =  false ? 0 : 1024;\n            let particleCount = count;\n            if (width < 640) particleCount = Math.min(count * 0.6, 8); // Mobile: fewer particles\n            else if (width < 1024) particleCount = Math.min(count * 0.8, 12); // Tablet: moderate particles\n            else particleCount = Math.min(count, 16); // Desktop: full count, max 16 for performance\n            for(let i = 0; i < particleCount; i++){\n                newParticles.push({\n                    id: i,\n                    x: Math.random() * 100,\n                    y: -10 - Math.random() * 100,\n                    size: 24 + Math.random() * 16,\n                    duration: 12 + Math.random() * 8,\n                    delay: Math.random() * 8,\n                    rotate: Math.random() * 360,\n                    icon: icons[Math.floor(Math.random() * icons.length)],\n                    opacity: 0.4 + Math.random() * 0.4,\n                    glowIntensity: 0.2 + Math.random() * 0.3,\n                    drift: (Math.random() - 0.5) * 40\n                });\n            }\n            setParticles(newParticles);\n        }\n    }[\"FallingIcons.useEffect\"], []); // Empty dependency array - only run once on mount\n    // Performance monitoring - reduce particles on low-end devices (one-time check)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FallingIcons.useEffect\": ()=>{\n            if (true) return;\n            // One-time performance check after particles are set\n            const checkPerformance = {\n                \"FallingIcons.useEffect.checkPerformance\": ()=>{\n                    if (navigator.deviceMemory && navigator.deviceMemory < 4) {\n                        // Device has less than 4GB RAM, reduce particles\n                        const reducedCount = Math.max(4, Math.floor(particles.length * 0.5));\n                        if (particles.length > reducedCount) {\n                            setParticles({\n                                \"FallingIcons.useEffect.checkPerformance\": (prev)=>prev.slice(0, reducedCount)\n                            }[\"FallingIcons.useEffect.checkPerformance\"]);\n                        }\n                    }\n                }\n            }[\"FallingIcons.useEffect.checkPerformance\"];\n            // Run check after a short delay to ensure particles are set\n            const timeoutId = setTimeout(checkPerformance, 100);\n            return ({\n                \"FallingIcons.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"FallingIcons.useEffect\"];\n        }\n    }[\"FallingIcons.useEffect\"], [\n        isClient,\n        particles.length\n    ]); // Include particles.length dependency\n    // Don't render anything during SSR or if no particles\n    if (!isClient || particles.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none overflow-hidden z-0\",\n        children: particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"falling-icon absolute select-none will-change-transform\",\n                style: {\n                    left: `${particle.x}%`,\n                    top: `${particle.y}%`,\n                    fontSize: `${particle.size}px`,\n                    color: `rgba(255, 255, 255, ${particle.opacity})`,\n                    textShadow: `0 0 ${particle.glowIntensity * 20}px rgba(147, 51, 234, ${particle.glowIntensity}), 0 0 ${particle.glowIntensity * 40}px rgba(219, 39, 119, ${particle.glowIntensity * 0.5})`,\n                    filter: `drop-shadow(0 0 ${particle.glowIntensity * 10}px rgba(147, 51, 234, 0.3))`,\n                    zIndex: 1\n                },\n                initial: {\n                    y: particle.y,\n                    x: particle.x,\n                    rotate: particle.rotate,\n                    opacity: 0,\n                    scale: 0.6\n                },\n                animate: {\n                    y: '120vh',\n                    x: particle.x + particle.drift,\n                    rotate: particle.rotate + 180,\n                    opacity: [\n                        0,\n                        particle.opacity * 0.3,\n                        particle.opacity,\n                        particle.opacity * 0.8,\n                        0\n                    ],\n                    scale: [\n                        0.6,\n                        0.9,\n                        1,\n                        0.9,\n                        0.6\n                    ]\n                },\n                transition: {\n                    duration: particle.duration,\n                    delay: particle.delay,\n                    repeat: Infinity,\n                    ease: 'easeInOut',\n                    opacity: {\n                        ease: 'easeInOut',\n                        times: [\n                            0,\n                            0.1,\n                            0.5,\n                            0.9,\n                            1\n                        ] // More controlled opacity transitions\n                    },\n                    scale: {\n                        ease: 'easeInOut',\n                        times: [\n                            0,\n                            0.2,\n                            0.5,\n                            0.8,\n                            1\n                        ]\n                    }\n                },\n                children: particle.icon\n            }, particle.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FallingIcons.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FallingIcons.jsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n// Memoize the component to prevent unnecessary re-renders\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(FallingIcons));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FallingIcons.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalBackgroundOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction GlobalBackgroundOverlay() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-[#0d0d17] z-[-5]\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\GlobalBackgroundOverlay.jsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9HbG9iYWxCYWNrZ3JvdW5kT3ZlcmxheS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLHFCQUNFO2tCQUVFLDRFQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBR3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFtZXJrXFxEb2N1bWVudHNcXGNhbGxzYXZlci5hcHBcXGZyb250ZW5kXFxhcHBcXGNvbXBvbmVudHNcXEdsb2JhbEJhY2tncm91bmRPdmVybGF5LmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2xvYmFsQmFja2dyb3VuZE92ZXJsYXkoKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBTb2xpZCBiYXNlIGJhY2tncm91bmQgLSBubyBleHRyYSBlbGVtZW50cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1bIzBkMGQxN10gei1bLTVdXCIgLz5cbiAgICA8Lz5cbiAgKTtcbn0gIl0sIm5hbWVzIjpbIkdsb2JhbEJhY2tncm91bmRPdmVybGF5IiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/GlobalBackgroundOverlay.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./app/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(ssr)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const supabaseClientRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Update scroll position for additional effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Check authentication status\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"Navbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Try to get the Supabase client\n                        try {\n                            supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                        } catch (error) {\n                            console.error('Error getting Supabase client:', error);\n                            supabaseClientRef.current = null;\n                        }\n                        // Check if we have a valid client\n                        if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                            const { data } = await supabaseClientRef.current.auth.getSession();\n                            setIsAuthenticated(!!data?.session);\n                        } else {\n                            console.error('Supabase client or auth not available');\n                            setIsAuthenticated(false);\n                        }\n                    } catch (error) {\n                        console.error('Error checking auth status:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Navbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Set up auth state listener with error handling\n            let subscription = null;\n            try {\n                // Only set up listener if we have a valid client\n                if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                    const authListener = supabaseClientRef.current.auth.onAuthStateChange({\n                        \"Navbar.useEffect.authListener\": (event, session)=>{\n                            setIsAuthenticated(!!session);\n                        }\n                    }[\"Navbar.useEffect.authListener\"]);\n                    if (authListener && authListener.data) {\n                        subscription = authListener.data.subscription;\n                    }\n                }\n            } catch (error) {\n                console.error('Error setting up auth listener:', error);\n            }\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    if (subscription && typeof subscription.unsubscribe === 'function') {\n                        subscription.unsubscribe();\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Determine if navbar should be more visible based on scroll\n    const isScrolled = scrollY > 50;\n    // Improved scroll function with better targeting and error handling\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            // Calculate header height to adjust scroll position\n            const navHeight = 80; // Approximate height of fixed navbar\n            const elementPosition = element.getBoundingClientRect().top;\n            const offsetPosition = elementPosition + window.pageYOffset - navHeight;\n            window.scrollTo({\n                top: offsetPosition,\n                behavior: 'smooth'\n            });\n        } else {\n            console.warn(`Section with ID \"${sectionId}\" not found`);\n        }\n        // Close the mobile menu after clicking a link\n        setIsMenuOpen(false);\n    };\n    // Handle sign out\n    const handleSignOut = async ()=>{\n        try {\n            // Try to get the Supabase client if we don't have it yet\n            if (!supabaseClientRef.current) {\n                try {\n                    supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                } catch (error) {\n                    console.error('Error getting Supabase client for sign out:', error);\n                }\n            }\n            // Check if we have a valid client\n            if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                await supabaseClientRef.current.auth.signOut();\n            }\n            // Clear any stored demo user\n            if (false) {}\n            router.push('/signin');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            // Still try to redirect even if there's an error\n            router.push('/signin');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `w-auto max-w-[95%] sm:max-w-[90%] md:max-w-4xl mx-auto flex items-center justify-between py-2 px-2 sm:px-3 md:px-4 fixed top-2 sm:top-4 left-0 right-0 z-50 rounded-full ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                style: {\n                    backgroundColor: isScrolled ? 'rgba(13, 13, 23, 0.65)' : 'rgba(13, 13, 23, 0.65)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: isScrolled ? '0 10px 25px rgba(0, 0, 0, 0.15), 0 0 30px rgba(139, 92, 246, 0.15)' : '0 8px 20px rgba(0, 0, 0, 0.1), 0 0 20px rgba(139, 92, 246, 0.1)',\n                    border: '1px solid rgba(255, 255, 255, 0.08)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: `flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative w-7 h-7 md:w-9 md:h-9 ${isRTL ? 'ml-1 md:ml-2' : 'mr-1 md:mr-2'} nav-logo`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-7 h-7 md:w-8 md:h-8 bg-purple-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 md:h-5 md:w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base md:text-lg font-bold text-white\",\n                                    children: \"CallSaver\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `hidden md:flex items-center justify-center mx-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-8`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Home\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('features'),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Features section\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('pricing'),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Pricing section\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/support\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Support page\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ml-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-2 md:space-x-3`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"block\",\n                                children: !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signin\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signup\",\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden flex-shrink-0 ml-1 md:ml-3 text-white p-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900 rounded-md\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 md:h-6 md:w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 md:hidden\",\n                onClick: ()=>setIsMenuOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black opacity-50\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 sm:top-20 right-2 sm:right-4 w-[calc(100%-1rem)] max-w-xs p-4 bg-gray-900/95 backdrop-blur-lg border border-purple-500/20 rounded-xl shadow-2xl z-50 flex flex-col space-y-2\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Home page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('features'),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Features section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Features\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('pricing'),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Pricing section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Pricing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/support\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Support page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Support\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Dashboard\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setIsMenuOpen(false);\n                                            handleSignOut();\n                                        },\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        \"aria-label\": \"Sign Out\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signin\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Sign In page\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sign In\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signup\",\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Get Started\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SafeMetaMaskDetection.js":
/*!*************************************************!*\
  !*** ./app/components/SafeMetaMaskDetection.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Safe MetaMask detection component that handles errors gracefully\n * Prevents console errors when MetaMask extension is not installed\n */ const SafeMetaMaskDetection = ({ children })=>{\n    const [hasMetaMask, setHasMetaMask] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SafeMetaMaskDetection.useEffect\": ()=>{\n            const checkMetaMask = {\n                \"SafeMetaMaskDetection.useEffect.checkMetaMask\": async ()=>{\n                    try {\n                        // Check if MetaMask is available\n                        if (false) {} else {\n                            setHasMetaMask(false);\n                        }\n                    } catch (error) {\n                        // Silently handle MetaMask detection errors\n                        console.debug('MetaMask detection failed (this is normal if MetaMask is not installed):', error.message);\n                        setHasMetaMask(false);\n                    } finally{\n                        setIsChecking(false);\n                    }\n                }\n            }[\"SafeMetaMaskDetection.useEffect.checkMetaMask\"];\n            // Add a small delay to ensure window is fully loaded\n            const timer = setTimeout(checkMetaMask, 100);\n            return ({\n                \"SafeMetaMaskDetection.useEffect\": ()=>clearTimeout(timer)\n            })[\"SafeMetaMaskDetection.useEffect\"];\n        }\n    }[\"SafeMetaMaskDetection.useEffect\"], []);\n    // Provide MetaMask context to children if needed\n    if (typeof children === 'function') {\n        return children({\n            hasMetaMask,\n            isChecking\n        });\n    }\n    return children;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeMetaMaskDetection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SafeMetaMaskDetection.js\n");

/***/ }),

/***/ "(ssr)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _locales_en_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locales/en.json */ \"(ssr)/./app/i18n/locales/en.json\");\n/* harmony import */ var _locales_de_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locales/de.json */ \"(ssr)/./app/i18n/locales/de.json\");\n/* harmony import */ var _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locales/ar.json */ \"(ssr)/./app/i18n/locales/ar.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\n\n\n// Language configurations\nconst LANGUAGES = {\n    en: {\n        code: 'en',\n        name: 'English',\n        dir: 'ltr',\n        translations: _locales_en_json__WEBPACK_IMPORTED_MODULE_2__,\n        flag: '🇬🇧'\n    },\n    de: {\n        code: 'de',\n        name: 'Deutsch',\n        dir: 'ltr',\n        translations: _locales_de_json__WEBPACK_IMPORTED_MODULE_3__,\n        flag: '🇩🇪'\n    },\n    ar: {\n        code: 'ar',\n        name: 'العربية',\n        dir: 'rtl',\n        translations: _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__,\n        flag: '🇦🇪'\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LANGUAGES.en);\n    // Detect user's language\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Function to detect language from navigator or localStorage\n            const detectLanguage = {\n                \"LanguageProvider.useEffect.detectLanguage\": ()=>{\n                    // Check if there's a stored preference\n                    const storedLang = localStorage.getItem('preferred-language');\n                    if (storedLang && LANGUAGES[storedLang]) {\n                        return LANGUAGES[storedLang];\n                    }\n                    // Detect browser language\n                    const browserLang = navigator.language.split('-')[0].toLowerCase();\n                    if (LANGUAGES[browserLang]) {\n                        return LANGUAGES[browserLang];\n                    }\n                    // Default to English\n                    return LANGUAGES.en;\n                }\n            }[\"LanguageProvider.useEffect.detectLanguage\"];\n            // Set the detected language\n            setLanguage(detectLanguage());\n            // Update document direction for RTL support\n            document.documentElement.dir = detectLanguage().dir;\n            if (detectLanguage().dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Update document direction when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            document.documentElement.dir = language.dir;\n            if (language.dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n            // Store the preference\n            localStorage.setItem('preferred-language', language.code);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Function to change language\n    const changeLanguage = (langCode)=>{\n        if (LANGUAGES[langCode]) {\n            setLanguage(LANGUAGES[langCode]);\n        }\n    };\n    // Helper function to get a translation by key path\n    const t = (keyPath)=>{\n        const keys = keyPath.split('.');\n        let value = language.translations;\n        for (const key of keys){\n            if (value && value[key]) {\n                value = value[key];\n            } else {\n                return keyPath; // Fallback to key if translation not found\n            }\n        }\n        return value;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            changeLanguage,\n            t,\n            languages: LANGUAGES,\n            isRTL: language.dir === 'rtl'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\i18n\\\\LanguageContext.jsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the language context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/i18n/LanguageContext.jsx\n");

/***/ }),

/***/ "(ssr)/./app/i18n/locales/ar.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/ar.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"دع الذكاء الاصطناعي يتعامل مع مكالماتك،","line2":"أنت تتعامل مع الحياة."},"subtitle":"متصل بسلاسة. أنت بلا جهد. يقوم حل الرسائل القصيرة الذكي الخاص بنا بإدارة مكالماتك الفائتة حتى تتمكن من التركيز على ما يهم.","buttons":{"trial":"تسجيل الدخول/التسجيل","pricing":"عرض الأسعار"},"footer":{"poweredBy":"مشغل بواسطة","businesses":"+5000 شركة"}},"navbar":{"features":"المميزات","pricing":"الأسعار","testimonials":"الشهادات","signin":"تسجيل الدخول/التسجيل","languages":{"english":"الإنجليزية","german":"الألمانية","arabic":"العربية"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/de.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/de.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Lassen Sie KI Ihre Anrufe verwalten,","line2":"Sie genießen das Leben."},"subtitle":"Nahtlos verbunden. Mühelos Sie. Unsere intelligente SMS-Lösung verwaltet Ihre verpassten Anrufe, damit Sie sich auf das Wesentliche konzentrieren können.","buttons":{"trial":"Anmelden/Registrieren","pricing":"Preise anzeigen"},"footer":{"poweredBy":"betrieben von","businesses":"5000+ Unternehmen"}},"navbar":{"features":"Funktionen","pricing":"Preisgestaltung","testimonials":"Erfahrungsberichte","signin":"Anmelden/Registrieren","languages":{"english":"Englisch","german":"Deutsch","arabic":"Arabisch"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/en.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/en.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Let AI Handle Your Calls,","line2":"You Handle Life."},"subtitle":"Seamlessly Connected. Effortlessly You. Our intelligent SMS solution manages your missed calls so you can focus on what matters.","buttons":{"trial":"Try 7 Days Free Trial","pricing":"View Pricing"},"footer":{"poweredBy":"powered by","businesses":"5000+ businesses"}},"navbar":{"features":"Features","pricing":"Pricing","testimonials":"Testimonials","signin":"Sign In/Up","languages":{"english":"English","german":"German","arabic":"Arabic"}}}');

/***/ }),

/***/ "(ssr)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* __next_internal_client_entry_do_not_use__ SessionProvider,useSession,default auto */ \n\n // Import the function\n// Create a context for the session\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction SessionProvider({ children }) {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const lastEventRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        type: null,\n        timestamp: 0\n    });\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionProvider.useEffect\": ()=>{\n            let mounted = true;\n            // Get the initial session\n            const getInitialSession = {\n                \"SessionProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        // Check for demo user in localStorage first\n                        const demoUser = localStorage.getItem('callsaver_demo_user');\n                        if (demoUser) {\n                            console.log('Demo user found in localStorage');\n                            const parsedUser = JSON.parse(demoUser);\n                            // Create a mock session for the demo user\n                            if (mounted) {\n                                setSession({\n                                    user: {\n                                        id: parsedUser.id,\n                                        email: parsedUser.email,\n                                        user_metadata: {\n                                            name: parsedUser.name,\n                                            role: parsedUser.role\n                                        }\n                                    },\n                                    expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now\n                                });\n                                setLoading(false);\n                            }\n                            return;\n                        }\n                        // Get the client instance\n                        const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                        if (!supabase) {\n                            console.error('Failed to get Supabase client in SessionProvider');\n                            if (mounted) setLoading(false);\n                            return; // Don't proceed without a client\n                        }\n                        // Try to get the session from Supabase\n                        const { data, error } = await supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting initial session:', error);\n                            if (mounted) setSession(null);\n                        } else {\n                            console.log('Initial session check:', data.session ? 'Session found' : 'No session');\n                            if (mounted) setSession(data.session);\n                        }\n                    } catch (err) {\n                        console.error('Unexpected error getting session:', err);\n                        if (mounted) setSession(null);\n                    } finally{\n                        if (mounted) setLoading(false);\n                    }\n                }\n            }[\"SessionProvider.useEffect.getInitialSession\"];\n            // Debounced session update function\n            const updateSession = {\n                \"SessionProvider.useEffect.updateSession\": (newSession, eventType)=>{\n                    if (debounceTimerRef.current) {\n                        clearTimeout(debounceTimerRef.current);\n                    }\n                    debounceTimerRef.current = setTimeout({\n                        \"SessionProvider.useEffect.updateSession\": ()=>{\n                            if (mounted) {\n                                setSession(newSession);\n                                setLoading(false);\n                                lastEventRef.current = {\n                                    type: eventType,\n                                    timestamp: Date.now()\n                                };\n                            }\n                        }\n                    }[\"SessionProvider.useEffect.updateSession\"], 100); // 100ms debounce\n                }\n            }[\"SessionProvider.useEffect.updateSession\"];\n            // Get the client instance for the listener\n            const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n            if (!supabase) {\n                console.error('Failed to get Supabase client for auth listener');\n                setLoading(false); // Ensure loading state is updated\n                return;\n            }\n            // Set up auth state listener with improved debouncing\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"SessionProvider.useEffect\": async (event, newSession)=>{\n                    // Skip if it's the same event type within 1 second\n                    const now = Date.now();\n                    const timeSinceLastEvent = now - lastEventRef.current.timestamp;\n                    if (event === lastEventRef.current.type && timeSinceLastEvent < 1000) {\n                        console.log('Skipping duplicate auth event:', event);\n                        return;\n                    }\n                    console.log('Auth state changed:', event);\n                    // Handle specific auth events\n                    switch(event){\n                        case 'SIGNED_IN':\n                            updateSession(newSession, event);\n                            break;\n                        case 'SIGNED_OUT':\n                            updateSession(null, event);\n                            break;\n                        case 'TOKEN_REFRESHED':\n                        case 'USER_UPDATED':\n                            if (newSession) {\n                                updateSession(newSession, event);\n                            }\n                            break;\n                        default:\n                            // For other events, only update if there's a meaningful change\n                            if (newSession?.user?.id !== session?.user?.id) {\n                                updateSession(newSession, event);\n                            }\n                            break;\n                    }\n                }\n            }[\"SessionProvider.useEffect\"]);\n            getInitialSession();\n            // Clean up\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    mounted = false;\n                    if (debounceTimerRef.current) {\n                        clearTimeout(debounceTimerRef.current);\n                    }\n                    subscription?.unsubscribe();\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    const value = {\n        session,\n        loading,\n        isAuthenticated: !!session,\n        user: session?.user || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use the session context\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error('useSession must be used within a SessionProvider');\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SessionProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./app/signin/page.js":
/*!****************************!*\
  !*** ./app/signin/page.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* harmony import */ var _actions_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/auth */ \"(ssr)/./app/actions/auth.js\");\n/* harmony import */ var _components_FallingIcons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/FallingIcons */ \"(ssr)/./app/components/FallingIcons.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction SignInPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';\n    const emailFromSignup = searchParams.get('email') || '';\n    const errorFromCallback = searchParams.get('error');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGoogleLoading, setIsGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: emailFromSignup,\n        password: ''\n    });\n    // Show message if coming from signup\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPage.useEffect\": ()=>{\n            if (emailFromSignup) {\n                const signupMessage = document.getElementById('signup-message');\n                if (signupMessage) {\n                    signupMessage.classList.remove('opacity-0');\n                    setTimeout({\n                        \"SignInPage.useEffect\": ()=>{\n                            signupMessage.classList.add('opacity-0');\n                        }\n                    }[\"SignInPage.useEffect\"], 5000);\n                }\n            }\n            // Handle error messages from callbacks\n            if (errorFromCallback) {\n                let errorMessage = 'An error occurred during authentication.';\n                if (errorFromCallback === 'auth_callback_error') {\n                    errorMessage = 'Failed to complete authentication. Please try again.';\n                } else if (errorFromCallback === 'missing_code') {\n                    errorMessage = 'Authentication code was missing. Please try again.';\n                } else if (errorFromCallback === 'unexpected_error') {\n                    errorMessage = 'An unexpected error occurred. Please try again.';\n                }\n                setError(errorMessage);\n            }\n        }\n    }[\"SignInPage.useEffect\"], [\n        emailFromSignup,\n        errorFromCallback\n    ]);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        setDebugInfo('Starting sign-in process via server action...');\n        try {\n            // Clear any stored auth state before attempting sign-in\n            localStorage.removeItem('supabase_auth');\n            // Use ONLY the server action for sign in\n            const result = await (0,_actions_auth__WEBPACK_IMPORTED_MODULE_5__.signIn)(formData.email, formData.password);\n            setDebugInfo((prev)=>prev + '\\nServer response: ' + JSON.stringify(result, null, 2));\n            if (!result.success) {\n                const serverError = result?.error;\n                if (!serverError) {\n                    setError('Authentication failed. Please try again.');\n                    setDebugInfo((prev)=>prev + '\\nUnexpected error format in response');\n                    setIsLoading(false);\n                    return;\n                }\n                setDebugInfo((prev)=>prev + `\\nServer-side sign-in error: ${serverError.message}` + `\\nError details: ${JSON.stringify(serverError, null, 2)}`);\n                if (serverError.message.includes('not confirmed') || serverError.message.includes('Email not confirmed')) {\n                    setError('Email not confirmed. Please check your email for the confirmation link.');\n                } else if (serverError.message.includes('Invalid login credentials')) {\n                    setError('Invalid email or password.');\n                } else {\n                    setError(serverError.message || 'An unknown login error occurred.');\n                }\n                setIsLoading(false);\n                return;\n            }\n            setDebugInfo((prev)=>prev + '\\nServer-side sign-in successful! Session should be set.');\n            // Set a flag in localStorage to indicate we just signed in\n            // This helps the dashboard layout know to retry session checks\n            localStorage.setItem('just_signed_in', 'true');\n            localStorage.setItem('sign_in_time', Date.now().toString());\n            // Clear any previous retry counts\n            localStorage.removeItem('auth_retry_count');\n            // No need for client-side checks or delays, server action handles cookies.\n            // Perform a full page reload to ensure cookies are read correctly by middleware/client.\n            window.location.assign(callbackUrl + '?just_signed_in=true');\n        } catch (err) {\n            setError('An unexpected error occurred during login. Please try again.');\n            console.error('Sign in error:', err);\n            setDebugInfo((prev)=>prev + '\\nUnexpected submit error: ' + err.message);\n            setIsLoading(false);\n        }\n    };\n    // Handle Google sign-in\n    const handleGoogleSignIn = async ()=>{\n        setIsGoogleLoading(true);\n        setError('');\n        setDebugInfo('Starting Google sign-in process...');\n        try {\n            // Call the server action for Google sign-in\n            const result = await (0,_actions_auth__WEBPACK_IMPORTED_MODULE_5__.signInWithGoogle)();\n            setDebugInfo((prev)=>prev + '\\nGoogle auth response: ' + JSON.stringify(result, null, 2));\n            if (!result.success) {\n                const serverError = result?.error;\n                setError(serverError?.message || 'Failed to authenticate with Google');\n                setDebugInfo((prev)=>prev + '\\nGoogle sign-in error: ' + (serverError?.message || 'Unknown error'));\n                setIsGoogleLoading(false);\n                return;\n            }\n            // Redirect to the OAuth URL provided by Supabase\n            if (result.url) {\n                setDebugInfo((prev)=>prev + '\\nRedirecting to Google OAuth URL: ' + result.url);\n                window.location.href = result.url;\n            } else {\n                setError('Authentication failed. No redirect URL provided.');\n                setIsGoogleLoading(false);\n            }\n        } catch (err) {\n            setError('An unexpected error occurred during Google sign-in.');\n            console.error('Google sign-in error:', err);\n            setDebugInfo((prev)=>prev + '\\nUnexpected Google sign-in error: ' + err.message);\n            setIsGoogleLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FallingIcons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 left-6 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/\",\n                    className: \"flex items-center text-white hover:text-purple-300 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        \"Back to Home\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"w-full max-w-md px-4\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-12 h-12 mr-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-6 w-6 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-bold text-white\",\n                                        children: \"CallSaver\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl text-white/80 mt-2\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8\",\n                        children: [\n                            emailFromSignup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"signup-message\",\n                                className: \"mb-6 p-3 bg-green-500/20 border border-green-500/30 rounded-lg text-center transition-opacity duration-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-200\",\n                                    children: \"Your account was created successfully! You can now sign in.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-3 bg-red-500/20 border border-red-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-200\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Email Address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                className: \"w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                                                placeholder: \"Enter your email\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"password\",\n                                                        className: \"block text-sm font-medium text-gray-400\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/forgot-password\",\n                                                        className: \"text-sm text-purple-400 hover:text-purple-300\",\n                                                        children: \"Forgot password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                value: formData.password,\n                                                onChange: handleChange,\n                                                className: \"w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                                                placeholder: \"Enter your password\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"laser-button w-full py-3 flex items-center justify-center\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                className: \"opacity-25\",\n                                                                cx: \"12\",\n                                                                cy: \"12\",\n                                                                r: \"10\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                className: \"opacity-75\",\n                                                                fill: \"currentColor\",\n                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Signing in...\"\n                                                ]\n                                            }, void 0, true) : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative my-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-600/50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-gray-900/70 text-gray-400\",\n                                            children: \"Or continue with\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleGoogleSignIn,\n                                disabled: isGoogleLoading,\n                                className: \"w-full flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-lg hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors\",\n                                children: isGoogleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Connecting to Google...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                                                    fill: \"#4285F4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                                                    fill: \"#34A853\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                                                    fill: \"#FBBC05\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                                                    fill: \"#EA4335\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign in with Google\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"Don't have an account?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/signup\",\n                                            className: \"text-purple-400 hover:text-purple-300 font-medium\",\n                                            children: \"Sign up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                             true && debugInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-3 bg-gray-800/70 border border-gray-700 rounded-lg overflow-auto max-h-60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 font-semibold mb-1 text-xs\",\n                                        children: \"Debug Info\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-gray-500 text-xs whitespace-pre-wrap\",\n                                        children: debugInfo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\signin\\\\page.js\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/signin/page.js\n");

/***/ }),

/***/ "(ssr)/./app/utils/supabaseClient.js":
/*!*************************************!*\
  !*** ./app/utils/supabaseClient.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ getSupabaseClient,default auto */ \n// Global variable to hold the singleton instance\nlet clientInstance = null;\n// Flag to track initialization in progress\nlet initializationInProgress = false;\n// Flag to indicate if an initialization attempt has been made\nlet initializationAttempted = false;\n// Create a simple mock client that won't throw errors\nconst createMockClient = (reason)=>{\n    console.warn(`Creating mock Supabase client: ${reason}`);\n    return {\n        auth: {\n            getSession: async ()=>({\n                    data: {\n                        session: null\n                    },\n                    error: null\n                }),\n            signInWithPassword: async ()=>({\n                    data: null,\n                    error: new Error(`Supabase client unavailable: ${reason}`)\n                }),\n            signOut: async ()=>({\n                    error: null\n                }),\n            onAuthStateChange: ()=>({\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>{}\n                        }\n                    }\n                })\n        },\n        // Add minimal implementations for other commonly used methods\n        from: ()=>({\n                select: ()=>({\n                        data: [],\n                        error: null\n                    }),\n                insert: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                update: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                delete: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    })\n            })\n    };\n};\n// Function to create or return the singleton Supabase client instance\nconst getSupabaseClient = ()=>{\n    // Return existing instance if already created and valid\n    if (clientInstance && clientInstance.auth && typeof clientInstance.auth.getSession === 'function') {\n        return clientInstance;\n    }\n    // If initialization is already in progress, wait for it to complete\n    if (initializationInProgress) {\n        throw new Error('Supabase client initialization in progress. Please retry your operation.');\n    }\n    // Ensure this runs only on the client\n    if (true) {\n        throw new Error('Supabase client can only be initialized in browser environment');\n    }\n    // Set flag to indicate we're attempting initialization\n    initializationInProgress = true;\n    initializationAttempted = true;\n    try {\n        const supabaseUrl = \"https://zzkytozgnociyjvhthfk.supabase.co\";\n        const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\";\n        if (!supabaseUrl || !supabaseAnonKey) {\n            initializationInProgress = false;\n            throw new Error('Supabase credentials are missing in environment variables');\n        }\n        // Create the actual client instance\n        const newClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                flowType: 'pkce',\n                detectSessionInUrl: true,\n                persistSession: true\n            }\n        });\n        // Validate the created client\n        if (!newClient || !newClient.auth || typeof newClient.auth.getSession !== 'function') {\n            throw new Error('Created client is invalid or missing auth methods');\n        }\n        // Set up auth state change listener for better debugging (optional here)\n        newClient.auth.onAuthStateChange((event, session)=>{\n            console.log('[getSupabaseClient] Auth state changed:', event, session ? 'Session exists' : 'No session');\n            // Example: Update local storage flag on sign-in/sign-out\n            // Ensure window check wraps localStorage access\n            if (false) {}\n        });\n        // Store the instance globally\n        clientInstance = newClient;\n        initializationInProgress = false;\n        console.log(\"Supabase client initialized successfully.\");\n        return clientInstance;\n    } catch (error) {\n        console.error('Failed to initialize Supabase client:', error);\n        clientInstance = null; // Ensure instance is null on error\n        initializationInProgress = false;\n        throw error; // Throw the error instead of returning a mock client\n    }\n};\n// Default export the function for easy import\n// Note: Files importing this will need to change from `import supabaseClient from ...`\n// to `import getSupabaseClient from ...` and call `getSupabaseClient()`\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSupabaseClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvdXRpbHMvc3VwYWJhc2VDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OytFQUVvRDtBQUVwRCxpREFBaUQ7QUFDakQsSUFBSUMsaUJBQWlCO0FBQ3JCLDJDQUEyQztBQUMzQyxJQUFJQywyQkFBMkI7QUFDL0IsOERBQThEO0FBQzlELElBQUlDLDBCQUEwQjtBQUU5QixzREFBc0Q7QUFDdEQsTUFBTUMsbUJBQW1CLENBQUNDO0lBQ3hCQyxRQUFRQyxJQUFJLENBQUMsQ0FBQywrQkFBK0IsRUFBRUYsUUFBUTtJQUN2RCxPQUFPO1FBQ0xHLE1BQU07WUFDSkMsWUFBWSxVQUFhO29CQUFFQyxNQUFNO3dCQUFFQyxTQUFTO29CQUFLO29CQUFHQyxPQUFPO2dCQUFLO1lBQ2hFQyxvQkFBb0IsVUFBYTtvQkFBRUgsTUFBTTtvQkFBTUUsT0FBTyxJQUFJRSxNQUFNLENBQUMsNkJBQTZCLEVBQUVULFFBQVE7Z0JBQUU7WUFDMUdVLFNBQVMsVUFBYTtvQkFBRUgsT0FBTztnQkFBSztZQUNwQ0ksbUJBQW1CLElBQU87b0JBQUVOLE1BQU07d0JBQUVPLGNBQWM7NEJBQUVDLGFBQWEsS0FBTzt3QkFBRTtvQkFBRTtnQkFBRTtRQUNoRjtRQUNBLDhEQUE4RDtRQUM5REMsTUFBTSxJQUFPO2dCQUNYQyxRQUFRLElBQU87d0JBQUVWLE1BQU0sRUFBRTt3QkFBRUUsT0FBTztvQkFBSztnQkFDdkNTLFFBQVEsSUFBTzt3QkFBRVgsTUFBTTt3QkFBTUUsT0FBTyxJQUFJRSxNQUFNLENBQUMsNkJBQTZCLEVBQUVULFFBQVE7b0JBQUU7Z0JBQ3hGaUIsUUFBUSxJQUFPO3dCQUFFWixNQUFNO3dCQUFNRSxPQUFPLElBQUlFLE1BQU0sQ0FBQyw2QkFBNkIsRUFBRVQsUUFBUTtvQkFBRTtnQkFDeEZrQixRQUFRLElBQU87d0JBQUViLE1BQU07d0JBQU1FLE9BQU8sSUFBSUUsTUFBTSxDQUFDLDZCQUE2QixFQUFFVCxRQUFRO29CQUFFO1lBQzFGO0lBQ0Y7QUFDRjtBQUVBLHNFQUFzRTtBQUMvRCxNQUFNbUIsb0JBQW9CO0lBQy9CLHdEQUF3RDtJQUN4RCxJQUFJdkIsa0JBQWtCQSxlQUFlTyxJQUFJLElBQUksT0FBT1AsZUFBZU8sSUFBSSxDQUFDQyxVQUFVLEtBQUssWUFBWTtRQUNqRyxPQUFPUjtJQUNUO0lBRUEsb0VBQW9FO0lBQ3BFLElBQUlDLDBCQUEwQjtRQUM1QixNQUFNLElBQUlZLE1BQU07SUFDbEI7SUFFQSxzQ0FBc0M7SUFDdEMsSUFBSSxJQUE2QixFQUFFO1FBQ2pDLE1BQU0sSUFBSUEsTUFBTTtJQUNsQjtJQUVBLHVEQUF1RDtJQUN2RFosMkJBQTJCO0lBQzNCQywwQkFBMEI7SUFFMUIsSUFBSTtRQUNGLE1BQU1zQixjQUFjQywwQ0FBb0M7UUFDeEQsTUFBTUcsa0JBQWtCSCxrTkFBeUM7UUFFakUsSUFBSSxDQUFDRCxlQUFlLENBQUNJLGlCQUFpQjtZQUNwQzNCLDJCQUEyQjtZQUMzQixNQUFNLElBQUlZLE1BQU07UUFDbEI7UUFFQSxvQ0FBb0M7UUFDcEMsTUFBTWlCLFlBQVkvQixrRUFBbUJBLENBQUN5QixhQUFhSSxpQkFBaUI7WUFDbEVyQixNQUFNO2dCQUNKd0IsVUFBVTtnQkFDVkMsb0JBQW9CO2dCQUNwQkMsZ0JBQWdCO1lBR2xCO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSSxDQUFDSCxhQUFhLENBQUNBLFVBQVV2QixJQUFJLElBQUksT0FBT3VCLFVBQVV2QixJQUFJLENBQUNDLFVBQVUsS0FBSyxZQUFZO1lBQ3BGLE1BQU0sSUFBSUssTUFBTTtRQUNsQjtRQUVBLHlFQUF5RTtRQUN6RWlCLFVBQVV2QixJQUFJLENBQUNRLGlCQUFpQixDQUFDLENBQUNtQixPQUFPeEI7WUFDdkNMLFFBQVE4QixHQUFHLENBQUMsMkNBQTJDRCxPQUFPeEIsVUFBVSxtQkFBbUI7WUFFM0YseURBQXlEO1lBQ3pELGdEQUFnRDtZQUNoRCxJQUFJLEtBQTZCLEVBQUUsRUFTbEM7UUFDSDtRQUVBLDhCQUE4QjtRQUM5QlYsaUJBQWlCOEI7UUFDakI3QiwyQkFBMkI7UUFDM0JJLFFBQVE4QixHQUFHLENBQUM7UUFDWixPQUFPbkM7SUFFVCxFQUFFLE9BQU9XLE9BQU87UUFDZE4sUUFBUU0sS0FBSyxDQUFDLHlDQUF5Q0E7UUFDdkRYLGlCQUFpQixNQUFNLG1DQUFtQztRQUMxREMsMkJBQTJCO1FBQzNCLE1BQU1VLE9BQU8scURBQXFEO0lBQ3BFO0FBQ0YsRUFBRTtBQUVGLDhDQUE4QztBQUM5Qyx1RkFBdUY7QUFDdkYsd0VBQXdFO0FBQ3hFLGlFQUFlWSxpQkFBaUJBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXGFwcFxcdXRpbHNcXHN1cGFiYXNlQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgY3JlYXRlQnJvd3NlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zc3InO1xuXG4vLyBHbG9iYWwgdmFyaWFibGUgdG8gaG9sZCB0aGUgc2luZ2xldG9uIGluc3RhbmNlXG5sZXQgY2xpZW50SW5zdGFuY2UgPSBudWxsO1xuLy8gRmxhZyB0byB0cmFjayBpbml0aWFsaXphdGlvbiBpbiBwcm9ncmVzc1xubGV0IGluaXRpYWxpemF0aW9uSW5Qcm9ncmVzcyA9IGZhbHNlO1xuLy8gRmxhZyB0byBpbmRpY2F0ZSBpZiBhbiBpbml0aWFsaXphdGlvbiBhdHRlbXB0IGhhcyBiZWVuIG1hZGVcbmxldCBpbml0aWFsaXphdGlvbkF0dGVtcHRlZCA9IGZhbHNlO1xuXG4vLyBDcmVhdGUgYSBzaW1wbGUgbW9jayBjbGllbnQgdGhhdCB3b24ndCB0aHJvdyBlcnJvcnNcbmNvbnN0IGNyZWF0ZU1vY2tDbGllbnQgPSAocmVhc29uKSA9PiB7XG4gIGNvbnNvbGUud2FybihgQ3JlYXRpbmcgbW9jayBTdXBhYmFzZSBjbGllbnQ6ICR7cmVhc29ufWApO1xuICByZXR1cm4ge1xuICAgIGF1dGg6IHtcbiAgICAgIGdldFNlc3Npb246IGFzeW5jICgpID0+ICh7IGRhdGE6IHsgc2Vzc2lvbjogbnVsbCB9LCBlcnJvcjogbnVsbCB9KSxcbiAgICAgIHNpZ25JbldpdGhQYXNzd29yZDogYXN5bmMgKCkgPT4gKHsgZGF0YTogbnVsbCwgZXJyb3I6IG5ldyBFcnJvcihgU3VwYWJhc2UgY2xpZW50IHVuYXZhaWxhYmxlOiAke3JlYXNvbn1gKSB9KSxcbiAgICAgIHNpZ25PdXQ6IGFzeW5jICgpID0+ICh7IGVycm9yOiBudWxsIH0pLFxuICAgICAgb25BdXRoU3RhdGVDaGFuZ2U6ICgpID0+ICh7IGRhdGE6IHsgc3Vic2NyaXB0aW9uOiB7IHVuc3Vic2NyaWJlOiAoKSA9PiB7fSB9IH0gfSlcbiAgICB9LFxuICAgIC8vIEFkZCBtaW5pbWFsIGltcGxlbWVudGF0aW9ucyBmb3Igb3RoZXIgY29tbW9ubHkgdXNlZCBtZXRob2RzXG4gICAgZnJvbTogKCkgPT4gKHtcbiAgICAgIHNlbGVjdDogKCkgPT4gKHsgZGF0YTogW10sIGVycm9yOiBudWxsIH0pLFxuICAgICAgaW5zZXJ0OiAoKSA9PiAoeyBkYXRhOiBudWxsLCBlcnJvcjogbmV3IEVycm9yKGBTdXBhYmFzZSBjbGllbnQgdW5hdmFpbGFibGU6ICR7cmVhc29ufWApIH0pLFxuICAgICAgdXBkYXRlOiAoKSA9PiAoeyBkYXRhOiBudWxsLCBlcnJvcjogbmV3IEVycm9yKGBTdXBhYmFzZSBjbGllbnQgdW5hdmFpbGFibGU6ICR7cmVhc29ufWApIH0pLFxuICAgICAgZGVsZXRlOiAoKSA9PiAoeyBkYXRhOiBudWxsLCBlcnJvcjogbmV3IEVycm9yKGBTdXBhYmFzZSBjbGllbnQgdW5hdmFpbGFibGU6ICR7cmVhc29ufWApIH0pXG4gICAgfSlcbiAgfTtcbn07XG5cbi8vIEZ1bmN0aW9uIHRvIGNyZWF0ZSBvciByZXR1cm4gdGhlIHNpbmdsZXRvbiBTdXBhYmFzZSBjbGllbnQgaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBnZXRTdXBhYmFzZUNsaWVudCA9ICgpID0+IHtcbiAgLy8gUmV0dXJuIGV4aXN0aW5nIGluc3RhbmNlIGlmIGFscmVhZHkgY3JlYXRlZCBhbmQgdmFsaWRcbiAgaWYgKGNsaWVudEluc3RhbmNlICYmIGNsaWVudEluc3RhbmNlLmF1dGggJiYgdHlwZW9mIGNsaWVudEluc3RhbmNlLmF1dGguZ2V0U2Vzc2lvbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBjbGllbnRJbnN0YW5jZTtcbiAgfVxuXG4gIC8vIElmIGluaXRpYWxpemF0aW9uIGlzIGFscmVhZHkgaW4gcHJvZ3Jlc3MsIHdhaXQgZm9yIGl0IHRvIGNvbXBsZXRlXG4gIGlmIChpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1N1cGFiYXNlIGNsaWVudCBpbml0aWFsaXphdGlvbiBpbiBwcm9ncmVzcy4gUGxlYXNlIHJldHJ5IHlvdXIgb3BlcmF0aW9uLicpO1xuICB9XG5cbiAgLy8gRW5zdXJlIHRoaXMgcnVucyBvbmx5IG9uIHRoZSBjbGllbnRcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdTdXBhYmFzZSBjbGllbnQgY2FuIG9ubHkgYmUgaW5pdGlhbGl6ZWQgaW4gYnJvd3NlciBlbnZpcm9ubWVudCcpO1xuICB9XG5cbiAgLy8gU2V0IGZsYWcgdG8gaW5kaWNhdGUgd2UncmUgYXR0ZW1wdGluZyBpbml0aWFsaXphdGlvblxuICBpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MgPSB0cnVlO1xuICBpbml0aWFsaXphdGlvbkF0dGVtcHRlZCA9IHRydWU7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTDtcbiAgICBjb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWTtcbiAgICBcbiAgICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgICAgIGluaXRpYWxpemF0aW9uSW5Qcm9ncmVzcyA9IGZhbHNlO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdTdXBhYmFzZSBjcmVkZW50aWFscyBhcmUgbWlzc2luZyBpbiBlbnZpcm9ubWVudCB2YXJpYWJsZXMnKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgdGhlIGFjdHVhbCBjbGllbnQgaW5zdGFuY2VcbiAgICBjb25zdCBuZXdDbGllbnQgPSBjcmVhdGVCcm93c2VyQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgZmxvd1R5cGU6ICdwa2NlJywgLy8gUmVjb21tZW5kZWQgZm9yIGJyb3dzZXIgY2xpZW50c1xuICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgICAgICAvLyBVc2luZyBkZWZhdWx0IGNvb2tpZSBzdG9yYWdlIGlzIGdlbmVyYWxseSByZWNvbW1lbmRlZCB3aXRoIEBzdXBhYmFzZS9zc3JcbiAgICAgICAgLy8gVGhlIGxpYnJhcnkgaGFuZGxlcyBjb29raWUgc3RvcmFnZSBhdXRvbWF0aWNhbGx5LlxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gVmFsaWRhdGUgdGhlIGNyZWF0ZWQgY2xpZW50XG4gICAgaWYgKCFuZXdDbGllbnQgfHwgIW5ld0NsaWVudC5hdXRoIHx8IHR5cGVvZiBuZXdDbGllbnQuYXV0aC5nZXRTZXNzaW9uICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NyZWF0ZWQgY2xpZW50IGlzIGludmFsaWQgb3IgbWlzc2luZyBhdXRoIG1ldGhvZHMnKTtcbiAgICB9XG5cbiAgICAvLyBTZXQgdXAgYXV0aCBzdGF0ZSBjaGFuZ2UgbGlzdGVuZXIgZm9yIGJldHRlciBkZWJ1Z2dpbmcgKG9wdGlvbmFsIGhlcmUpXG4gICAgbmV3Q2xpZW50LmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoKGV2ZW50LCBzZXNzaW9uKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW2dldFN1cGFiYXNlQ2xpZW50XSBBdXRoIHN0YXRlIGNoYW5nZWQ6JywgZXZlbnQsIHNlc3Npb24gPyAnU2Vzc2lvbiBleGlzdHMnIDogJ05vIHNlc3Npb24nKTtcblxuICAgICAgLy8gRXhhbXBsZTogVXBkYXRlIGxvY2FsIHN0b3JhZ2UgZmxhZyBvbiBzaWduLWluL3NpZ24tb3V0XG4gICAgICAvLyBFbnN1cmUgd2luZG93IGNoZWNrIHdyYXBzIGxvY2FsU3RvcmFnZSBhY2Nlc3NcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICBpZiAoZXZlbnQgPT09ICdTSUdORURfSU4nKSB7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2p1c3Rfc2lnbmVkX2luJywgJ3RydWUnKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnc2lnbl9pbl90aW1lJywgRGF0ZS5ub3coKS50b1N0cmluZygpKTtcbiAgICAgICAgfSBlbHNlIGlmIChldmVudCA9PT0gJ1NJR05FRF9PVVQnKSB7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2p1c3Rfc2lnbmVkX2luJyk7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3NpZ25faW5fdGltZScpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoX3JldHJ5X2NvdW50Jyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFN0b3JlIHRoZSBpbnN0YW5jZSBnbG9iYWxseVxuICAgIGNsaWVudEluc3RhbmNlID0gbmV3Q2xpZW50O1xuICAgIGluaXRpYWxpemF0aW9uSW5Qcm9ncmVzcyA9IGZhbHNlO1xuICAgIGNvbnNvbGUubG9nKFwiU3VwYWJhc2UgY2xpZW50IGluaXRpYWxpemVkIHN1Y2Nlc3NmdWxseS5cIik7XG4gICAgcmV0dXJuIGNsaWVudEluc3RhbmNlO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgU3VwYWJhc2UgY2xpZW50OicsIGVycm9yKTtcbiAgICBjbGllbnRJbnN0YW5jZSA9IG51bGw7IC8vIEVuc3VyZSBpbnN0YW5jZSBpcyBudWxsIG9uIGVycm9yXG4gICAgaW5pdGlhbGl6YXRpb25JblByb2dyZXNzID0gZmFsc2U7XG4gICAgdGhyb3cgZXJyb3I7IC8vIFRocm93IHRoZSBlcnJvciBpbnN0ZWFkIG9mIHJldHVybmluZyBhIG1vY2sgY2xpZW50XG4gIH1cbn07XG5cbi8vIERlZmF1bHQgZXhwb3J0IHRoZSBmdW5jdGlvbiBmb3IgZWFzeSBpbXBvcnRcbi8vIE5vdGU6IEZpbGVzIGltcG9ydGluZyB0aGlzIHdpbGwgbmVlZCB0byBjaGFuZ2UgZnJvbSBgaW1wb3J0IHN1cGFiYXNlQ2xpZW50IGZyb20gLi4uYFxuLy8gdG8gYGltcG9ydCBnZXRTdXBhYmFzZUNsaWVudCBmcm9tIC4uLmAgYW5kIGNhbGwgYGdldFN1cGFiYXNlQ2xpZW50KClgXG5leHBvcnQgZGVmYXVsdCBnZXRTdXBhYmFzZUNsaWVudDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY2xpZW50SW5zdGFuY2UiLCJpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MiLCJpbml0aWFsaXphdGlvbkF0dGVtcHRlZCIsImNyZWF0ZU1vY2tDbGllbnQiLCJyZWFzb24iLCJjb25zb2xlIiwid2FybiIsImF1dGgiLCJnZXRTZXNzaW9uIiwiZGF0YSIsInNlc3Npb24iLCJlcnJvciIsInNpZ25JbldpdGhQYXNzd29yZCIsIkVycm9yIiwic2lnbk91dCIsIm9uQXV0aFN0YXRlQ2hhbmdlIiwic3Vic2NyaXB0aW9uIiwidW5zdWJzY3JpYmUiLCJmcm9tIiwic2VsZWN0IiwiaW5zZXJ0IiwidXBkYXRlIiwiZGVsZXRlIiwiZ2V0U3VwYWJhc2VDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsIm5ld0NsaWVudCIsImZsb3dUeXBlIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwicGVyc2lzdFNlc3Npb24iLCJldmVudCIsImxvZyIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJyZW1vdmVJdGVtIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/utils/supabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ClientServiceWorkerManager.js */ \"(ssr)/./app/components/ClientServiceWorkerManager.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConditionalNavbar.jsx */ \"(ssr)/./app/components/ConditionalNavbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConsoleErrorSuppressor.js */ \"(ssr)/./app/components/ConsoleErrorSuppressor.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/GlobalBackgroundOverlay.jsx */ \"(ssr)/./app/components/GlobalBackgroundOverlay.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SafeMetaMaskDetection.js */ \"(ssr)/./app/components/SafeMetaMaskDetection.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(ssr)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(ssr)/./app/providers/SessionProvider.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/signin/page.js */ \"(ssr)/./app/signin/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDY2FsbHNhdmVyLmFwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDc2lnbmluJTVDJTVDcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcY2FsbHNhdmVyLmFwcFxcXFxmcm9udGVuZFxcXFxhcHBcXFxcc2lnbmluXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Csignin%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignin%2Fpage&page=%2Fsignin%2Fpage&appPaths=%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fsignin%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();